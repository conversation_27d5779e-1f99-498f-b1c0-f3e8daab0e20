# BitBucket Copy Env Docker Image
# Supports both command line arguments and environment variables for authentication
FROM rust as builder
COPY . /opt/bb-copy-env
COPY ./docker-entrypoint.sh /docker-entrypoint.sh
WORKDIR /opt/bb-copy-env
RUN chmod +x /docker-entrypoint.sh \
    && cargo build -r && cp target/release/bb-copy-env /usr/local/bin

# Environment variables for secure authentication (optional):
# BITBUCKET_USERNAME - Atlassian account email address
# BITBUCKET_API_TOKEN - BitBucket API token
ENTRYPOINT ["/docker-entrypoint.sh"]