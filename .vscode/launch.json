{"version": "0.2.0", "configurations": [{"type": "lldb", "request": "launch", "name": "Debug executable 'bb-copy-env'", "cargo": {"args": ["build", "--bin=bb-copy-env", "--package=bb-copy-env"], "filter": {"name": "bb-copy-env", "kind": "bin"}}, "args": ["-t", "************************************************************************************************************************************************************************************************", "-r", "geoscan-laravel"], "cwd": "${workspaceFolder}"}, {"type": "lldb", "request": "launch", "name": "Debug unit tests in executable 'bb-copy-env'", "cargo": {"args": ["test", "--no-run", "--bin=bb-copy-env", "--package=bb-copy-env"], "filter": {"name": "bb-copy-env", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}"}]}