use base64::{engine::general_purpose, Engine as _};
use reqwest::header::{HeaderMap, HeaderValue, AUTHORIZATION, CONTENT_TYPE};
use anyhow::Result;

/// Credentials for Bitbucket API authentication
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct BitbucketCredentials {
    pub username: String,
    pub api_token: String,
}

impl BitbucketCredentials {
    /// Create new credentials
    pub fn new(username: String, api_token: String) -> Self {
        Self { username, api_token }
    }
    
    /// Generate HTTP Basic Authentication header value
    pub fn to_basic_auth_header(&self) -> Result<HeaderValue> {
        let credentials = format!("{}:{}", self.username, self.api_token);
        let encoded = general_purpose::STANDARD.encode(credentials.as_bytes());
        let auth_value = format!("Basic {}", encoded);
        
        HeaderValue::from_str(&auth_value)
            .map_err(|e| anyhow::anyhow!("Failed to create authorization header: {}", e))
    }
    
    /// Create headers for JSON requests with Basic Authentication
    pub fn create_json_headers(&self) -> Result<HeaderMap> {
        let mut headers = HeaderMap::new();
        headers.insert(AUTHORIZATION, self.to_basic_auth_header()?);
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));
        Ok(headers)
    }
    
    /// Create headers for GET requests with Basic Authentication
    pub fn create_get_headers(&self) -> Result<HeaderMap> {
        let mut headers = HeaderMap::new();
        headers.insert(AUTHORIZATION, self.to_basic_auth_header()?);
        Ok(headers)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_basic_auth_header_generation() {
        let creds = BitbucketCredentials::new(
            "<EMAIL>".to_string(),
            "test_token".to_string()
        );
        
        let header = creds.to_basic_auth_header().unwrap();
        let header_str = header.to_str().unwrap();
        
        // The expected base64 encoding of "<EMAIL>:test_token"
        assert!(header_str.starts_with("Basic "));
        
        // Decode and verify
        let encoded_part = header_str.strip_prefix("Basic ").unwrap();
        let decoded = general_purpose::STANDARD.decode(encoded_part).unwrap();
        let decoded_str = String::from_utf8(decoded).unwrap();
        
        assert_eq!(decoded_str, "<EMAIL>:test_token");
    }
    
    #[test]
    fn test_json_headers_creation() {
        let creds = BitbucketCredentials::new(
            "<EMAIL>".to_string(),
            "test_token".to_string()
        );
        
        let headers = creds.create_json_headers().unwrap();
        
        assert!(headers.contains_key(AUTHORIZATION));
        assert!(headers.contains_key(CONTENT_TYPE));
        assert_eq!(headers.get(CONTENT_TYPE).unwrap(), "application/json");
    }
}
