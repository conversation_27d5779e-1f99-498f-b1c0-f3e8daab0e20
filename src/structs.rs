use serde::{Deserialize, Serialize};
use serde_with::serde_as;
use std::fmt;

#[derive(De<PERSON>ult, Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PaginatedResponse<T> {
    pub page: i64,
    pub values: Vec<T>,
    pub size: i64,
    pub pagelen: i64,
    pub next: Option<String>,
    pub previous: Option<String>,
}

#[derive(Default, Debug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct EnvironmentValue {
    #[serde(rename = "type")]
    pub type_field: String,
    pub uuid: String,
    pub name: String,
}

impl fmt::Display for EnvironmentValue {
    fn fmt(&self, f: &mut fmt::Formatter) -> Result<(), fmt::Error> {
        write!(f, "{}", self.name)
    }
}

#[serde_as]
#[derive(Default, Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>q, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct VariableValue {
    #[serde(rename = "type")]
    pub type_field: String,
    pub uuid: String,
    pub key: String,
    #[serde(default)]
    pub value: String,
    pub secured: bool,
}

impl fmt::Display for VariableValue {
    fn fmt(&self, f: &mut fmt::Formatter) -> Result<(), fmt::Error> {
        let mut lock = "";
        if self.secured {
            lock = "🔒";
        }
        write!(f, "{}{}={}", lock, self.key, self.value)
    }
}

#[derive(Default, Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeploymentEnvironment {
    #[serde(rename = "deployment_gate_enabled")]
    pub deployment_gate_enabled: bool,
    pub rank: i64,
    pub hidden: bool,
    #[serde(rename = "type")]
    pub type_field: String,
    pub slug: String,
    #[serde(rename = "environment_type")]
    pub environment_type: DeploymentEnvironmentType,
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub uuid: Option<String>,
}

#[derive(Default, Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeploymentEnvironmentType {
    #[serde(rename = "type")]
    pub type_field: String,
    pub name: String,
    pub rank: i64,
}

#[derive(Default, Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeploymentVariable {
    pub key: String,
    pub value: String,
    pub secured: bool,
}

#[derive(Default, Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Repository {
    #[serde(rename = "type")]
    pub type_field: String,
    pub uuid: String,
    #[serde(rename = "full_name")]
    pub full_name: String,
    #[serde(rename = "is_private")]
    pub is_private: bool,
    pub scm: String,
    pub name: String,
    pub description: String,
    #[serde(rename = "created_on")]
    pub created_on: String,
    #[serde(rename = "updated_on")]
    pub updated_on: String,
    pub size: i64,
    pub language: String,
    #[serde(rename = "has_issues")]
    pub has_issues: bool,
    #[serde(rename = "has_wiki")]
    pub has_wiki: bool,
    #[serde(rename = "fork_policy")]
    pub fork_policy: String,
}

impl fmt::Display for Repository {
    fn fmt(&self, f: &mut fmt::Formatter) -> Result<(), fmt::Error> {
        write!(
            f,
            "{} ({})",
            self.name,
            self.full_name.split('/').last().unwrap()
        )
    }
}
