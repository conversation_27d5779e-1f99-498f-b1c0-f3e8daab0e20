use clap::<PERSON><PERSON><PERSON>;
use std::env;

#[derive(Parser, Debug)]
pub struct Args {
    /// Atlassian account email address (username for Basic Auth). Can also be set via BITBUCKET_USERNAME environment variable.
    #[arg(short, long, env = "BITBUCKET_USERNAME")]
    pub username: Option<String>,

    /// BitBucket API token (password for Basic Auth). Can also be set via BITBUCKET_API_TOKEN environment variable.
    #[arg(short = 't', long = "api-token", env = "BITBUCKET_API_TOKEN")]
    pub api_token: Option<String>,

    /// A BitBucket Workspace.
    #[arg(short, long, default_value_t = String::from("technofarm"))]
    pub workspace: String,

    /// Show verbose output
    #[arg(short, long, default_value_t = false)]
    pub verbose: bool,
}

impl Args {
    /// Get username from args or environment variable, with validation
    pub fn get_username(&self) -> Result<String, String> {
        self.username
            .clone()
            .or_else(|| env::var("BITBUCKET_USERNAME").ok())
            .ok_or_else(|| "Username is required. Provide via --username argument or BITBUCKET_USERNAME environment variable.".to_string())
    }

    /// Get API token from args or environment variable, with validation
    pub fn get_api_token(&self) -> Result<String, String> {
        self.api_token
            .clone()
            .or_else(|| env::var("BITBUCKET_API_TOKEN").ok())
            .ok_or_else(|| "API token is required. Provide via --api-token argument or BITBUCKET_API_TOKEN environment variable.".to_string())
    }
}
