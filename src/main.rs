mod args;
mod auth;
mod structs;

use crate::{
    args::Args,
    auth::BitbucketCredentials,
    structs::{
        DeploymentEnvironment, DeploymentEnvironmentType, EnvironmentValue, PaginatedResponse,
        Repository, VariableValue,
    },
};
use anyhow::{<PERSON>rro<PERSON>, Ok, Result};
use clap::Parser;
use colored::Colorize;
use indicatif::{ProgressBar, ProgressStyle};
use inquire::{required, ui::RenderConfig, Confirm, Password, Select, Text};
use reqwest::{
    blocking::{Client, Response},
    Method,
};
use serde::Serialize;

enum RequestData<T: Serialize + Clone> {
    Json(T),
    NoData,
}
fn main() -> Result<()> {
    let args = Args::parse();

    let base_url: String = "https://api.bitbucket.org/2.0".to_string();
    let workspace: String = args.workspace.clone();

    // Get credentials with proper error handling
    let username = args.get_username().map_err(|e| anyhow::anyhow!(e))?;
    let api_token = args.get_api_token().map_err(|e| anyhow::anyhow!(e))?;
    let credentials = BitbucketCredentials::new(username, api_token);

    let client = reqwest::blocking::Client::new();
    const NO_DATA: RequestData<()> = RequestData::NoData;
    let repositories_list_url = format!("{}/repositories/{}", base_url, workspace);

    let reps_result = make_request(
        &client,
        Method::GET,
        &repositories_list_url,
        &credentials,
        NO_DATA,
        args.verbose,
    )?
    .json::<PaginatedResponse<Repository>>();

    let reps = reps_result.unwrap();

    let selected_rep = Select::new("Choose repository:", reps.values)
        .prompt()
        .unwrap();

    let repo_slug: String = selected_rep
        .full_name
        .split('/')
        .last()
        .unwrap()
        .to_string();

    let envs_url = format!(
        "{}/repositories/{}/{}/environments/",
        base_url, workspace, repo_slug
    );

    let envs_result = make_request(
        &client,
        Method::GET,
        &envs_url,
        &credentials,
        NO_DATA,
        args.verbose,
    )?
    .json::<PaginatedResponse<EnvironmentValue>>();
    let envs: PaginatedResponse<EnvironmentValue> = envs_result.unwrap();

    let selected_env = Select::new("Choose environment to copy from:", envs.values)
        .prompt()
        .unwrap();
    let target_env_types = vec!["Staging", "Production", "Test"];
    let selected_target_env_type = Select::new("Target env type:", target_env_types)
        .prompt()
        .unwrap();

    let new_env_name = Text::new("The new env name?")
        .with_validator(required!("This field is required"))
        .prompt()
        .unwrap();

    let mut env_vars_options: Vec<VariableValue> = load_all_depl_vars(
        &client,
        &credentials,
        &base_url,
        &workspace,
        &repo_slug,
        &selected_env.uuid,
        100,
    );

    loop {
        let continue_edit = Confirm {
            message: "Edit variable?",
            default: Some(false),
            placeholder: Some("y/N"),
            help_message: Some("It's alright not to"),
            formatter: &|ans| match ans {
                true => "y".to_owned(),
                false => "N".to_owned(),
            },
            parser: &|ans| match ans {
                "y" => Result::Ok(true),
                "N" => Result::Ok(false),
                _ => Err(()),
            },
            error_message: "Reply with 'y' or 'N'".into(),
            default_value_formatter: &|def| match def {
                true => String::from("y"),
                false => String::from("N"),
            },
            render_config: RenderConfig::default(),
        }
        .prompt()
        .unwrap();

        if !continue_edit {
            break;
        }

        let selected_var = Select::new("Choose variable to edit:", env_vars_options.clone())
            .prompt()
            .unwrap();

        env_vars_options = env_vars_options
            .into_iter()
            .map(|v| {
                if v.uuid == selected_var.uuid {
                    return VariableValue {
                        value: Text::new("New value:")
                            .with_default(&selected_var.key)
                            .prompt()
                            .unwrap(),
                        ..v
                    };
                }
                v
            })
            .collect::<Vec<VariableValue>>();
    }

    let create_env_url = format!(
        "{}/repositories/{}/{}/environments/",
        base_url, workspace, repo_slug
    );

    let create_env_params = DeploymentEnvironment {
        deployment_gate_enabled: false,
        rank: 0,
        hidden: false,
        type_field: "deployment_environment".to_string(),
        slug: new_env_name.to_string(),
        environment_type: DeploymentEnvironmentType {
            type_field: "deployment_environment_type".to_string(),
            name: selected_target_env_type.to_string(),
            rank: 1,
        },
        name: new_env_name,
        uuid: None,
    };
    let create_env_result = make_request::<DeploymentEnvironment>(
        &client,
        Method::POST,
        &create_env_url,
        &credentials,
        RequestData::Json(create_env_params),
        args.verbose,
    )?
    .json::<DeploymentEnvironment>();

    let mut sec_env_vars_options: Vec<VariableValue> = env_vars_options
        .iter()
        .filter(|&var| var.secured)
        .cloned()
        .collect::<Vec<VariableValue>>();

    let mut notsec_env_vars_options = env_vars_options
        .iter()
        .filter(|&var| !var.secured)
        .cloned()
        .collect::<Vec<VariableValue>>();

    match !sec_env_vars_options.is_empty() {
        true => {
            let sec_var_label =
                "The following variables will be skipped because they are secured.".yellow();
            println!("{}", sec_var_label);

            sec_env_vars_options.iter_mut().for_each(|var_key_value| {
                let set_value_for_label =
                    format!("Set value for \"{}\"?", var_key_value.key.cyan());
                let continue_edit = Confirm {
                    message: &set_value_for_label,
                    default: Some(false),
                    placeholder: Some("y/N"),
                    help_message: Some("It's alright not to."),
                    formatter: &|ans| match ans {
                        true => "y".to_owned(),
                        false => "N".to_owned(),
                    },
                    parser: &|ans| match ans {
                        "y" => Result::Ok(true),
                        "N" => Result::Ok(false),
                        _ => Err(()),
                    },
                    error_message: "Reply with 'y' or 'N'".into(),
                    default_value_formatter: &|def| match def {
                        true => String::from("y"),
                        false => String::from("N"),
                    },
                    render_config: RenderConfig::default(),
                }
                .prompt()
                .unwrap();
                if continue_edit {
                    let env_var_name_label = format!("{}=", var_key_value.key.cyan());
                    let sec_value = Password::new(&env_var_name_label)
                        .with_help_message("Pres [Ctr+R] to toggle password view.")
                        .with_display_toggle_enabled()
                        .without_confirmation()
                        .prompt()
                        .unwrap();
                    var_key_value.value = sec_value;
                    notsec_env_vars_options.push(var_key_value.clone());
                    return;
                }
            });
        }
        false => (),
    }

    let progress_bar = ProgressBar::new(notsec_env_vars_options.len().try_into().unwrap());
    progress_bar.set_style(
        ProgressStyle::with_template(
            "{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {msg} {percent}%",
        )
        .unwrap()
        .progress_chars("#>-"),
    );

    let new_env_uuid = create_env_result.unwrap().uuid.unwrap();
    let create_vars_url = format!(
        "{}/repositories/{}/{}/deployments_config/environments/{}/variables",
        base_url, workspace, repo_slug, new_env_uuid
    );
    for var_key_value in notsec_env_vars_options {
        make_request(
            &client,
            Method::POST,
            &create_vars_url.to_string(),
            &credentials,
            RequestData::Json(&var_key_value),
            args.verbose,
        )?;

        progress_bar.set_message(var_key_value.key);
        progress_bar.inc(1);
    }
    progress_bar.finish();
    return Ok(());
}

fn make_request<T: Serialize + Clone>(
    client: &Client,
    method: reqwest::Method,
    url: &String,
    credentials: &BitbucketCredentials,
    json: RequestData<T>,
    verbose: bool,
) -> Result<Response, Error> {
    let r = match json {
        RequestData::Json(j) => {
            let json_data = serde_json::to_string(&j).unwrap();
            if verbose {
                println!("{} {}", "Calling:".yellow(), url.magenta());
                println!(
                    "{} \n{}",
                    "POST Data:".yellow(),
                    serde_json::to_string_pretty::<T>(&j).unwrap().magenta()
                )
            }
            let headers_map = credentials
                .create_json_headers()
                .map_err(|e| anyhow::anyhow!("Failed to create headers: {}", e))?;

            client
                .request(method, url)
                .headers(headers_map)
                .body(json_data)
                .send()?
        }
        RequestData::NoData => {
            if verbose {
                println!("{} {}", "Calling:".yellow(), url.magenta());
            }
            let headers_map = credentials
                .create_get_headers()
                .map_err(|e| anyhow::anyhow!("Failed to create headers: {}", e))?;

            client.request(method, url).headers(headers_map).send()?
        }
    };

    if r.status().is_success() {
        Ok(r)
    } else {
        Err(Error::from(r.error_for_status().unwrap_err()))
    }
}

fn calc_num_pages(total_items: i64, per_page: Option<i64>) -> i64 {
    let per_page_val = match per_page {
        Some(v) => v,
        None => 100,
    };
    let result: i64 = total_items / per_page_val;

    return result;
}

fn load_depl_vars(
    client: &Client,
    credentials: &BitbucketCredentials,
    base_url: &String,
    workspace: &String,
    repo_slug: &String,
    env_uuid: &String,
    page: i64,
    per_page: i64,
) -> PaginatedResponse<VariableValue> {
    let vars_url = format!(
        "{}/repositories/{}/{}/deployments_config/environments/{}/variables?page={}&pagelen={}",
        base_url,
        workspace,
        repo_slug,
        env_uuid,
        page.to_string(),
        per_page.to_string()
    );
    let headers_map = credentials.create_get_headers().unwrap();
    let vars_response = client.get(vars_url).headers(headers_map).send().unwrap();

    let vars_result = vars_response
        .json::<PaginatedResponse<VariableValue>>()
        .unwrap();

    return vars_result;
}
fn load_all_depl_vars(
    client: &Client,
    credentials: &BitbucketCredentials,
    base_url: &String,
    workspace: &String,
    repo_slug: &String,
    env_uuid: &String,
    per_page: i64,
) -> Vec<VariableValue> {
    let mut env_vars_options: Vec<VariableValue> = Vec::new();
    let vars_result: PaginatedResponse<VariableValue> = load_depl_vars(
        client,
        credentials,
        base_url,
        workspace,
        repo_slug,
        env_uuid,
        1,
        per_page,
    );
    env_vars_options.extend(vars_result.values);

    let pages = 2 + calc_num_pages(vars_result.size, Some(per_page));
    for n in 2..pages {
        env_vars_options.extend(
            load_depl_vars(
                client,
                credentials,
                base_url,
                workspace,
                repo_slug,
                env_uuid,
                n,
                per_page,
            )
            .values,
        )
    }

    return env_vars_options;
}
