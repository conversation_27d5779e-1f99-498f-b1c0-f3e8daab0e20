# BitBucket Copy Env
This tool copies all environment variables from one deployment environment to another.
The user is guided through the process.

## Authentication

This tool uses **HTTP Basic Authentication** with the Bitbucket API. You need:

1. **Username**: Your Atlassian account email address
2. **API Token**: A Bitbucket API token (not your password)

### Creating a Bitbucket API Token

1. Go to your Bitbucket account settings
2. Navigate to "App passwords" or "API tokens"
3. Create a new token with appropriate permissions for repository access
4. Use this token as the API token for authentication

## Usage

### Command Line Arguments
```
Usage: bb-copy-env [OPTIONS]

Options:
  -u, --username <USERNAME>        Atlassian account email address (username for Basic Auth)
  -t, --api-token <API_TOKEN>      BitBucket API token (password for Basic Auth)
  -w, --workspace <WORKSPACE>      A BitBucket Workspace [default: technofarm]
  -v, --verbose                    Show verbose output
  -h, --help                       Print help information
```

### Environment Variables (Recommended)

For security, it's recommended to use environment variables instead of command line arguments:

```bash
export BITBUCKET_USERNAME="<EMAIL>"
export BITBUCKET_API_TOKEN="your-api-token"
bb-copy-env
```

### Mixed Usage

You can also mix command line arguments and environment variables:

```bash
export BITBUCKET_API_TOKEN="your-api-token"
bb-copy-env --username "<EMAIL>"
```

## Build
To build the project you need a local installation of Rust and to execute the commnad bellow. After that the executable will be in `target/release/`.

```cargo build -r```

## Run
To build and run execute the command bellow:

```cargo run```

## Security Best Practices

1. **Never commit credentials to version control**
2. **Use environment variables** instead of command line arguments when possible
3. **Rotate API tokens regularly**
4. **Use minimal permissions** when creating API tokens
5. **Store tokens securely** (e.g., in a password manager or secure environment)

## Docker

The command can be run from a Docker container. Use environment variables for secure credential passing:

```bash
# Build the image
docker build -t bb-copy-env .

# Run with environment variables (recommended)
docker run --rm -it \
  -e BITBUCKET_USERNAME="<EMAIL>" \
  -e BITBUCKET_API_TOKEN="your-api-token" \
  bb-copy-env

# Or run with command line arguments
docker run --rm -it bb-copy-env \
  --username "<EMAIL>" \
  --api-token "your-api-token"
```